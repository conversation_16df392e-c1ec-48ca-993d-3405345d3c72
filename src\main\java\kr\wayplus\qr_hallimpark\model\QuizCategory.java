package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 문제 카테고리 모델
 * - quiz_category 테이블에 대응
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizCategory {

    /**
     * 카테고리 고유 ID
     */
    private Long categoryId;

    /**
     * 카테고리명
     * - 사용자가 직접 입력하는 카테고리명
     * - 최대 100자까지 입력 가능
     */
    private String categoryName;

    /**
     * 카테고리 설명
     */
    private String description;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private java.time.LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private java.time.LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private java.time.LocalDateTime deleteDate;
}
