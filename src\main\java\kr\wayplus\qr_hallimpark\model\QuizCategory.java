package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 문제 카테고리 모델
 * - quiz_category 테이블에 대응
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizCategory {
    
    /**
     * 카테고리 고유 ID
     */
    private Long categoryId;
    
    /**
     * 카테고리명 (ENUM)
     * - HISTORY: 역사
     * - TRADITIONAL_CULTURAL_HERITAGE: 전통 문화유산
     * - PLACES_AND_PEOPLE: 장소 및 인물
     * - NATURAL_SCIENCE: 자연 과학
     * - GENERAL_KNOWLEDGE: 상식
     * - HUMOR: 유머
     * - TOURIST_SPOT_CUSTOMIZED: 관광지 맞춤형
     */
    private CategoryName categoryName;
    
    /**
     * 카테고리 설명
     */
    private String description;
    
    /**
     * 카테고리명 ENUM
     */
    public enum CategoryName {
        HISTORY("역사"),
        TRADITIONAL_CULTURAL_HERITAGE("전통 문화유산"),
        PLACES_AND_PEOPLE("장소 및 인물"),
        NATURAL_SCIENCE("자연 과학"),
        GENERAL_KNOWLEDGE("상식"),
        HUMOR("유머"),
        TOURIST_SPOT_CUSTOMIZED("관광지 맞춤형");
        
        private final String displayName;
        
        CategoryName(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        /**
         * 표시명으로 CategoryName 찾기
         * @param displayName 표시명
         * @return CategoryName 또는 null
         */
        public static CategoryName fromDisplayName(String displayName) {
            for (CategoryName categoryName : values()) {
                if (categoryName.getDisplayName().equals(displayName)) {
                    return categoryName;
                }
            }
            return null;
        }
    }
}
