<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 카테고리 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="문제 카테고리를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>
                            <i class="fas fa-tags"></i>콘텐츠 카테고리 관리
                        </h2>
                        <p>문제의 카테고리를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>새 카테고리 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 카테고리 목록 -->
                <div class="row">
                    <!-- 카테고리가 있는 경우 -->
                    <div th:if="${categories != null and !categories.isEmpty()}" class="col-12">
                        <div class="row" id="categoryList">
                            <div th:each="category : ${categories}" class="col-lg-4 col-md-6">
                                <div class="card category-card" data-category-card th:data-category-id="${category.categoryId}">
                                    <div class="card-content">
                                        <div class="category-header">
                                            <h5 class="category-name" data-category-name th:text="${category.categoryName.displayName}">카테고리명</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}">
                                                            <i class="fas fa-edit"></i>수정
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item"
                                                                th:data-delete-btn="${category.categoryId}"
                                                                th:onclick="|deleteCategory(${category.categoryId}, '${category.categoryName.displayName}')|">
                                                            <i class="fas fa-trash"></i>삭제
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                        <p class="category-description" data-category-description
                                           th:text="${category.description != null and !category.description.isEmpty() ? category.description : '설명이 없습니다.'}">
                                            카테고리 설명
                                        </p>

                                        <div class="category-footer">
                                            <span>
                                                ID: <span th:text="${category.categoryId}" data-category-id-display>1</span>
                                            </span>
                                            <div>
                                                <a th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}"
                                                   class="btn btn-outline-primary btn-action" data-edit-btn>
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-danger btn-action"
                                                        th:data-delete-btn-small="${category.categoryId}"
                                                        th:onclick="|deleteCategory(${category.categoryId}, '${category.categoryName.displayName}')|">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 카테고리가 없는 경우 -->
                    <div th:if="${categories == null or categories.isEmpty()}" class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-question-circle"></i>
                            <h3>등록된 문제 카테고리가 없습니다</h3>
                            <p class="mb-4">새로운 문제 카테고리를 등록해보세요.</p>
                            <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>첫 번째 카테고리 등록하기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script th:src="@{/js/manage/quiz-category.js}"></script>
        <script>
            // 페이지 로드 시 초기화
            document.addEventListener('DOMContentLoaded', function() {
                QuizCategoryManager.init();
            });
        </script>
    </th:block>
</body>
</html>
