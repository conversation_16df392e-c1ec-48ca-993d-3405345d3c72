<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 페이지별 타이틀 -->
    <title th:text="${pageTitle != null ? pageTitle + ' - 한림공원 QR 체험' : '오류 - 한림공원 QR 체험'}">오류 - 한림공원 QR 체험</title>
    
    <!-- 메타 태그 -->
    <meta name="description" th:content="${pageDescription != null ? pageDescription : '요청 처리 중 오류가 발생했습니다.'}">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- 파비콘 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/common/base.css" rel="stylesheet">
    
    <!-- 오류 페이지 전용 스타일 -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
            margin: 2rem;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .error-description {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .error-actions .btn {
            min-width: 200px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .error-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .error-details {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin: 1.5rem 0;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        @media (min-width: 576px) {
            .error-actions {
                flex-direction: row;
                justify-content: center;
                flex-wrap: wrap;
            }
        }
        
        /* 애니메이션 효과 */
        .error-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .error-icon {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
    
    <!-- 추가 헤드 콘텐츠 -->
    <th:block layout:fragment="head"></th:block>
</head>
<body>
    <!-- 오류 페이지 콘텐츠 -->
    <div class="error-container">
        <th:block layout:fragment="content"></th:block>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- 추가 스크립트 -->
    <th:block layout:fragment="scripts"></th:block>
    
    <!-- 자동 새로고침 방지 및 뒤로가기 처리 -->
    <script>
        // 뒤로가기 버튼 처리
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        
        // 새로고침 처리
        function reloadPage() {
            window.location.reload();
        }
        
        // 홈으로 이동
        function goHome() {
            window.location.href = '/';
        }
    </script>
</body>
</html>
