<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}">
<head>
    <title>페이지를 찾을 수 없습니다 - 한림공원 QR 체험</title>
    <meta name="description" content="요청하신 페이지를 찾을 수 없습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-search text-primary error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-primary">404</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title" th:text="${errorMessage != null ? errorMessage : '페이지를 찾을 수 없습니다'}">페이지를 찾을 수 없습니다</h1>
        <p class="error-description">
            요청하신 페이지가 존재하지 않거나 이동되었을 수 있습니다.<br>
            URL을 다시 확인해주세요.
        </p>

        <!-- 요청 URL 표시 (개발 환경에서만) -->
        <div class="error-details" th:if="${requestUrl != null}">
            <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <a th:href="@{/}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="goBack()">
                <i class="fas fa-arrow-left me-2"></i>이전 페이지로
            </button>
        </div>
    </div>
</body>
</html>
