package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.model.QuizCategory;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 문제 카테고리 매퍼
 * - quiz_category 테이블 CRUD 처리
 */
@Mapper
@Repository
public interface QuizCategoryMapper {
    
    /**
     * 모든 문제 카테고리 목록 조회
     * @return 문제 카테고리 목록
     */
    List<QuizCategory> selectQuizCategoryList();
    
    /**
     * 카테고리 ID로 문제 카테고리 조회
     * @param categoryId 카테고리 ID
     * @return 문제 카테고리 정보
     */
    QuizCategory selectQuizCategoryById(Long categoryId);
    
    /**
     * 카테고리명으로 문제 카테고리 조회
     * @param categoryName 카테고리명
     * @return 문제 카테고리 정보
     */
    QuizCategory selectQuizCategoryByName(String categoryName);
    
    /**
     * 문제 카테고리 등록
     * @param quizCategory 문제 카테고리 정보
     * @return 등록된 행 수
     */
    int insertQuizCategory(QuizCategory quizCategory);
    
    /**
     * 문제 카테고리 수정
     * @param quizCategory 문제 카테고리 정보
     * @return 수정된 행 수
     */
    int updateQuizCategory(QuizCategory quizCategory);
    
    /**
     * 문제 카테고리 삭제 (소프트 삭제)
     * @param quizCategory 삭제할 카테고리 정보 (categoryId, deleteId 포함)
     * @return 삭제된 행 수
     */
    int deleteQuizCategory(QuizCategory quizCategory);
    
    /**
     * 카테고리명 중복 체크
     * @param categoryName 카테고리명
     * @return 중복 개수
     */
    int countByCategoryName(String categoryName);
    
    /**
     * 카테고리명 중복 체크 (수정 시 자기 자신 제외)
     * @param categoryName 카테고리명
     * @param categoryId 제외할 카테고리 ID
     * @return 중복 개수
     */
    int countByCategoryNameExcludeId(String categoryName, Long categoryId);
}
