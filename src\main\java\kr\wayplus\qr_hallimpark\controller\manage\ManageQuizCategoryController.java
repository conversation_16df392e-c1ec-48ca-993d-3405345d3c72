package kr.wayplus.qr_hallimpark.controller.manage;

import kr.wayplus.qr_hallimpark.model.QuizCategory;
import kr.wayplus.qr_hallimpark.service.QuizCategoryService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 관리자 문제 카테고리 컨트롤러
 * - 문제 카테고리 관리 페이지 및 API 처리
 */
@Controller
@RequestMapping("/manage/quiz-category")
@RequiredArgsConstructor
public class ManageQuizCategoryController {
    
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final QuizCategoryService quizCategoryService;
    
    /**
     * 문제 카테고리 목록 페이지
     */
    @GetMapping({"", "/"})
    public String categoryList(Model model) {
        logger.debug("Quiz category list page requested");
        
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        try {
            List<QuizCategory> quizCategories = quizCategoryService.findAllQuizCategories();
            
            // 페이지 메타 정보 설정
            model.addAttribute("pageTitle", "문제 카테고리 관리");
            model.addAttribute("pageDescription", "문제 카테고리를 관리합니다.");
            model.addAttribute("username", auth.getName());
            model.addAttribute("quizCategories", quizCategories);
            
            return "manage/quiz-category/list";
            
        } catch (Exception e) {
            logger.error("Error loading quiz category list: {}", e.getMessage(), e);
            model.addAttribute("errorMessage", "카테고리 목록을 불러오는 중 오류가 발생했습니다.");
            return "manage/quiz-category/list";
        }
    }
    
    /**
     * 문제 카테고리 등록 페이지
     */
    @GetMapping("/new")
    public String categoryForm(Model model) {
        logger.debug("Quiz category form page requested");
        
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        // 페이지 메타 정보 설정
        model.addAttribute("pageTitle", "문제 카테고리 등록");
        model.addAttribute("pageDescription", "새로운 문제 카테고리를 등록합니다.");
        model.addAttribute("username", auth.getName());
        model.addAttribute("isEdit", false);
        
        return "manage/quiz-category/form";
    }
    
    /**
     * 문제 카테고리 수정 페이지
     */
    @GetMapping("/{categoryId}/edit")
    public String categoryEditForm(@PathVariable Long categoryId, Model model) {
        logger.debug("Quiz category edit form page requested. ID: {}", categoryId);
        
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        try {
            QuizCategory quizCategory = quizCategoryService.findQuizCategoryById(categoryId);
            
            // 페이지 메타 정보 설정
            model.addAttribute("pageTitle", "문제 카테고리 수정");
            model.addAttribute("pageDescription", "문제 카테고리 정보를 수정합니다.");
            model.addAttribute("username", auth.getName());
            model.addAttribute("quizCategory", quizCategory);
            model.addAttribute("isEdit", true);
            
            return "manage/quiz-category/form";
            
        } catch (Exception e) {
            logger.error("Error loading quiz category for edit: {}", e.getMessage(), e);
            model.addAttribute("errorMessage", "카테고리 정보를 불러오는 중 오류가 발생했습니다.");
            return "redirect:/manage/quiz-category";
        }
    }
    
    /**
     * 문제 카테고리 등록 처리
     */
    @PostMapping("/add")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createCategory(@RequestBody QuizCategory quizCategory) {
        logger.debug("Creating quiz category: {}", quizCategory);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> response = new HashMap<>();

        try {
            // 생성자 정보 설정
            quizCategory.setCreateId(auth.getName());

            QuizCategory createdCategory = quizCategoryService.createQuizCategory(quizCategory);
            
            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 등록되었습니다.");
            response.put("data", createdCategory);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid quiz category data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            logger.error("Error creating quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 등록 중 오류가 발생했습니다.");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 문제 카테고리 수정 처리 (API)
     */
    @PutMapping("/api/{categoryId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateCategory(@PathVariable Long categoryId, @RequestBody QuizCategory quizCategory) {
        logger.debug("Updating quiz category. ID: {}, Data: {}", categoryId, quizCategory);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> response = new HashMap<>();

        try {
            // 수정자 정보 설정
            quizCategory.setCategoryId(categoryId);
            quizCategory.setLastUpdateId(auth.getName());

            QuizCategory updatedCategory = quizCategoryService.updateQuizCategory(quizCategory);
            
            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 수정되었습니다.");
            response.put("data", updatedCategory);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid quiz category data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            logger.error("Error updating quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 수정 중 오류가 발생했습니다.");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 문제 카테고리 삭제 처리 (API)
     */
    @DeleteMapping("/api/{categoryId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteCategory(@PathVariable Long categoryId) {
        logger.debug("Deleting quiz category. ID: {}", categoryId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> response = new HashMap<>();

        try {
            quizCategoryService.deleteQuizCategory(categoryId, auth.getName());

            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 삭제되었습니다.");

            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid category ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            logger.error("Error deleting quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 삭제 중 오류가 발생했습니다.");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 문제 카테고리 상세 조회 (API)
     */
    @GetMapping("/api/{categoryId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getCategory(@PathVariable Long categoryId) {
        logger.debug("Getting quiz category. ID: {}", categoryId);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            QuizCategory category = quizCategoryService.findQuizCategoryById(categoryId);
            
            response.put("success", true);
            response.put("data", category);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid category ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            logger.error("Error getting quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 조회 중 오류가 발생했습니다.");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 문제 카테고리 목록 조회 (API)
     */
    @GetMapping("/api/list")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getCategoryList() {
        logger.debug("Getting quiz category list");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<QuizCategory> categories = quizCategoryService.findAllQuizCategories();
            
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error getting quiz category list: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 목록 조회 중 오류가 발생했습니다.");
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
