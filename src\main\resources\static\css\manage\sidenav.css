/* 관리자 사이드 네비게이션 스타일 */

/* 관리자 레이아웃 기본 설정 */
.manage-layout {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* 사이드네비 기본 스타일 */
.sidenav {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    z-index: 1050;
    transform: translateX(0);
    transition: transform 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

/* 사이드네비 숨김 상태 */
.sidenav.collapsed {
    transform: translateX(-280px);
}

/* 사이드네비 헤더 */
.sidenav-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidenav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidenav-logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.sidenav-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ecf0f1;
}

.sidenav-toggle {
    background: none;
    border: none;
    color: #ecf0f1;
    font-size: 1.25rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.sidenav-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 사이드네비 바디 */
.sidenav-body {
    padding: 1rem 0;
    flex: 1;
}

/* 사용자 정보 */
.sidenav-user {
    padding: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.8rem;
    color: #bdc3c7;
}

/* 메뉴 스타일 */
.sidenav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    margin-bottom: 0.25rem;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.menu-link.active {
    background: linear-gradient(90deg, #3498db, #2980b9);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.menu-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ffffff;
}

.menu-icon {
    width: 20px;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 1rem;
}

.menu-text {
    flex: 1;
    font-weight: 500;
}

.menu-arrow {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.menu-item.open .menu-arrow {
    transform: rotate(90deg);
}

/* 서브메뉴 스타일 */
.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.2);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.menu-item.open .submenu {
    max-height: 300px;
}

.submenu-item {
    margin: 0;
}

.submenu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem 0.75rem 3rem;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.submenu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
}

.submenu-link.active {
    background-color: rgba(52, 152, 219, 0.3);
    color: #3498db;
    border-left: 3px solid #3498db;
}

.submenu-icon {
    width: 16px;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.submenu-text {
    font-weight: 400;
}

/* 사이드네비 푸터 */
.sidenav-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.logout-form {
    margin: 0;
}

.btn-logout {
    width: 100%;
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-logout:hover {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: #ffffff;
}

/* 오버레이 (모바일용) */
.sidenav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidenav-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 메인 래퍼 및 콘텐츠 조정 */
.main-wrapper {
    margin-left: 280px;
    transition: margin-left 0.3s ease;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-wrapper.expanded {
    margin-left: 0;
}

.main-content {
    flex: 1;
    padding: 0;
}

/* 헤더 조정 */
.manage-header {
    background: #ffffff;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1030;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
}

.sidenav-toggle-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #495057;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.sidenav-toggle-btn:hover {
    background-color: #f8f9fa;
}

.header-title h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.header-actions .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    transition: all 0.3s ease;
}

.header-actions .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

/* 반응형 디자인 */
@media (max-width: 991.98px) {
    .sidenav {
        transform: translateX(-280px);
    }

    .sidenav.show {
        transform: translateX(0);
    }

    .main-wrapper {
        margin-left: 0;
    }
}

/* 스크롤바 스타일 */
.sidenav::-webkit-scrollbar {
    width: 6px;
}

.sidenav::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidenav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidenav::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
