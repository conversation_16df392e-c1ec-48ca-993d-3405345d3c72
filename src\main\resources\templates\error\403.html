<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>접근 권한 없음 - 한림공원 QR 체험</title>
    <meta name="description" content="해당 페이지에 접근할 권한이 없습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-lock text-warning error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-warning">403</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">접근 권한이 없습니다</h1>
        <p class="error-description">
            해당 페이지에 접근할 권한이 없습니다.<br>
            로그인이 필요하거나 관리자 권한이 필요할 수 있습니다.
        </p>

        <!-- 요청 URL 표시 (개발 환경에서만) -->
        <div class="error-details" th:if="${requestUrl != null}">
            <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
        </div>

        <!-- 로그인 상태에 따른 다른 메시지 -->
        <div sec:authorize="!isAuthenticated()" class="alert alert-info border-0 mb-4">
            <h6 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>로그인이 필요합니다
            </h6>
            <p class="mb-0">
                해당 페이지를 이용하시려면 먼저 로그인해주세요.
            </p>
        </div>

        <div sec:authorize="isAuthenticated()" class="alert alert-warning border-0 mb-4">
            <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>권한이 부족합니다
            </h6>
            <p class="mb-0">
                현재 계정으로는 해당 페이지에 접근할 수 없습니다.
                관리자에게 문의해주세요.
            </p>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <!-- 비로그인 사용자용 버튼 -->
            <div sec:authorize="!isAuthenticated()">
                <a th:href="@{/user/login}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>로그인하기
                </a>
            </div>
            <a th:href="@{/}" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
        </div>
    </div>
</body>
</html>
