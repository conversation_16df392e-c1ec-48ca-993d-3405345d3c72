<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/user}">
<head>
    <title>오류 발생 - 한림공원 QR 체험</title>
    <meta name="description" content="요청 처리 중 오류가 발생했습니다.">
</head>
<body>
    <div layout:fragment="content">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 col-md-10">
                    <div>
                        <!-- 에러 아이콘 -->
                        <div>
                            <i class="fas fa-exclamation-circle text-danger" style="font-size: 6rem;"></i>
                        </div>

                        <!-- 에러 코드 (동적) -->
                        <h1 class="display-1 text-danger" th:text="${errorCode != null ? errorCode : 'ERROR'}">ERROR</h1>

                        <!-- 에러 메시지 -->
                        <h2 class="h3">오류가 발생했습니다</h2>
                        <p class="text-muted" th:text="${errorMessage != null ? errorMessage : '요청 처리 중 예상치 못한 오류가 발생했습니다.'}">
                            요청 처리 중 예상치 못한 오류가 발생했습니다.
                        </p>

                        <!-- 요청 URL 표시 (개발 환경에서만) -->
                        <div class="alert alert-light border" th:if="${requestUrl != null}">
                            <span class="text-muted">
                                <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
                            </span>
                        </div>

                        <!-- 에러 상세 정보 (개발 환경에서만) -->
                        <div class="alert alert-warning border" th:if="${detail != null and #strings.length(detail) > 0}">
                            <details>
                                <summary class="text-muted">상세 정보 보기</summary>
                                <pre class="text-start" th:text="${detail}"></pre>
                            </details>
                        </div>

                        <!-- 액션 버튼들 -->
                        <div class="d-flex flex-column flex-sm-row">
                            <button type="button" class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo"></i>다시 시도하기
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="fas fa-arrow-left"></i>이전 페이지로
                            </button>
                            <a th:href="@{/}" class="btn btn-outline-primary">
                                <i class="fas fa-home"></i>홈으로 돌아가기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
