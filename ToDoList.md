### **Phase 1: 핵심 기반 및 공통 모듈 구현**
- [x] `GlobalExceptionHandler` 구현 (`@ControllerAdvice`)
    - [x] Thymeleaf 뷰 요청용 HTML 에러 페이지 렌더링 메서드
    - [x] API 요청용 JSON 에러 응답 메서드
- [x] 공통 HTML 에러 페이지 생성 (`/templates/error/404.html`, `500.html`, `403.html`, `error.html`)
- [x] Thymeleaf 공통 레이아웃 구현
    - [x] `fragments`를 이용한 `header`, `footer` 조각 생성
    - [x] 기본 레이아웃 템플릿 (`layout/default.html`) 구현
- [x] 공통 CSS 및 JavaScript 파일 생성 (`common.css`, `common.js`)

### **Phase 2: 관리자 시스템 기능 구현**
- [ ] **대시보드 구현**
    - [ ] 실시간 참여 현황, 정답률 등 주요 지표 시각화 페이지
- [ ] **사용자 및 그룹 관리**
    - [ ] 개인 사용자 목록 조회 및 관리 기능
    - [ ] 단체(그룹) 및 하위 팀(반) CRUD 기능
    - [ ] 단체 소속 멤버 일괄 등록 기능 (엑셀 Import)
- [ ] **콘텐츠(문제) 관리**
    - [ ] 문제 카테고리 관리 CRUD 기능
    - [ ] 문제 CRUD 기능 (6가지 문제 유형 모두 지원)
        - [ ] 객관식, OX 퀴즈 유형 문제 생성 폼 구현
        - [ ] 순서 정렬, 퍼즐/기억력 게임 유형 문제 생성 폼 구현 (프론트엔드 복잡도 높음)
        - [ ] 이미지/음성 인식 유형 문제 생성 폼 구현
    - [ ] 문제별 번역 데이터 입력 및 관리 UI 구현 (다국어 지원)
- [ ] **QR 코드 관리 (서버 A(외부 QR코드 서비스) - IFrame 연동 대상)**
    - [ ] QR 코드 생성, 수정, 삭제(논리적) 기능
    - [ ] QR 코드 상태 관리 기능 (생성됨, 인쇄됨, 설치됨, 비활성화됨)
    - [ ] QR 코드 목록 조회 및 검색 기능
    - [ ] **(핵심)** 지금 프로젝트(서버 B)에서 호출할 `GET /api/qrcodes` API 엔드포인트 구현 (CORS 설정 필수)
- [ ] **QR-문제 연결 기능 구현**
    - [ ] **(서버 A)** QR 관리 시스템에 `GET /api/qrcodes` API 엔드포인트 구현 (CORS 설정 필수)
    - [ ] **(서버 B)** 문제 수정 페이지에 **[+ QR 찾아보기]** 버튼 및 모달 창 UI 구현
    - [ ] **(서버 B)** 모달 창의 JavaScript에서 서버 A의 `/api/qrcodes` API를 호출하여 QR 목록을 동적으로 표시
    - [ ] **(서버 B)** 모달에서 특정 QR 선택 시, 해당 `qr_id`와 `quiz_id`를 백엔드로 전송하여 연결 관계를 저장하는 API 및 로직 구현
    - [ ] **(서버 B)** 문제 수정 페이지에 현재 연결된 QR 목록을 표시하는 기능 구현
- [ ] **스토리형 런케이션 관리**
    - [ ] 스토리라인 CRUD 기능
    - [ ] 스토리라인 단계(Step) 편집 기능 (순서 변경, QR 및 퀴즈 연결)
- [ ] **현장 관리자용 모바일 페이지**
    - [ ] QR 코드 상태 현장 확인 및 오류 리포트 기능 (사진 첨부 포함)
    - [ ] 오류 리포트 제출 시, 업무 메신저(슬랙 등)로 알림 발송 기능

### **Phase 3: 사용자 서비스 기능 구현**
- [ ] **공통 기능**
    - [ ] QR 스캔 처리 엔드포인트 구현 (`GET /s/{qrId}`)
    - [ ] 최초 방문자 닉네임 입력 및 사용자 세션 관리 기능
    - [ ] 언어 선택 기능 및 UI/콘텐츠 다국어 적용
    - [ ] 탐색 보조 지도 UI 구현 (QR 위치, 편의시설 표시)
- [ ] **개인전 모드**
    - [ ] 문제 풀이 페이지 구현 (`quiz/play.html`)
    - [ ] 답안 제출 및 결과 처리 API 구현 (`POST /api/quiz/submit`)
    - [ ] 정답/오답에 따른 피드백(애니메이션, 점수) 동적 표시
- [ ] **단체전 모드**
    - [ ] 단체 코드 입력 및 팀 선택 기능
    - [ ] 실시간 랭킹 보드 페이지 구현 (JavaScript 주기적 업데이트)
    - [ ] 단체 맞춤형 문제 및 환영 메시지 표시 로직
- [ ] **스토리 모드**
    - [ ] 순차적 문제 진행 로직 구현
    - [ ] 스토리 전용 UI 및 내러티브 표시
    - [ ] 사용자별 스토리 진행 상태 기록 및 이어하기 기능
    - [ ] 스토리 완주 시, 완료 화면 및 보상 안내 표시
- [ ] **마이페이지 기능**
    - [ ] 나의 활동 이력(푼 문제, 점수) 조회 페이지
    - [ ] 획득한 인증서 목록 조회 및 보기

### **Phase 4: 고급 기능 및 고도화**
- [ ] **인증서 및 보상 시스템**
    - [ ] 서버 사이드에서 인증서 이미지 동적 생성 기능
    - [ ] 인증서 SNS 공유 기능 (Open Graph 메타 태그 최적화)
    - [ ] 상품 수령 등 오프라인 보상 연동 로직
- [ ] **AI 기능**
    - [ ] AI 캐릭터 안내 멘트 음성 합성(TTS) 기능 (생성된 음성 파일 캐싱 처리)
    - [ ] (선택적 고도화) 이미지/음성 인식 문제 유형 실제 API 연동
- [ ] **통계 및 분석**
    - [ ] 관리자 대시보드를 위한 통계 데이터 집계 로직 (참여율, 정답률 등)
    - [ ] 콘텐츠 반응 분석 및 피드백 수집 기능