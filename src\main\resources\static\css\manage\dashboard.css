/* 한림공원 QR 체험 - 관리자 대시보드 스타일 */

/* 대시보드 전체 레이아웃 */
.dashboard-container {
    padding: 1.5rem;
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
}

/* 페이지 헤더 */
.page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    padding: 2rem 1.5rem;
    border-radius: 0;
}

.page-header h2 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header .text-muted {
    color: #6c757d !important;
    font-size: 1rem;
}

/* 대시보드 카드 */
.dashboard-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    background: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 통계 카드 */
.stat-card {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

/* 통계 숫자 */
.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 통계 라벨 */
.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* 통계 아이콘 */
.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* 관리 메뉴 카드 스타일 */
.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-card .card-body i {
    margin-bottom: 1.5rem;
    display: block;
}

.dashboard-card h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.dashboard-card .text-muted {
    color: #6c757d !important;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.dashboard-card .btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dashboard-card .btn:hover {
    transform: translateY(-2px);
}

/* 차트 컨테이너 */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 차트 헤더 */
.chart-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

/* 최근 활동 */
.recent-activity {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.activity-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.activity-icon.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.activity-icon.info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 빠른 액션 */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quick-action-btn {
    display: block;
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    color: #2d3748;
    text-decoration: none;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    border-color: #2E8B57;
    background-color: rgba(46, 139, 87, 0.05);
    color: #2E8B57;
    text-decoration: none;
    transform: translateY(-2px);
}

.quick-action-btn:last-child {
    margin-bottom: 0;
}

.quick-action-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

/* 대시보드 메인 콘텐츠 영역 조정 */
.main-content .container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* 통계 카드 그리드 */
.row.g-4 {
    margin-bottom: 2rem;
}

/* 카드 그림자 효과 개선 */
.card.border-0.shadow-sm {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    border-radius: 15px !important;
}

.card-header.bg-white {
    background-color: #ffffff !important;
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.card-header h5 {
    color: #2c3e50;
    font-weight: 600;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }

    .page-header {
        margin: -1rem -1rem 1.5rem -1rem;
        padding: 1.5rem 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-icon {
        font-size: 2.5rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .chart-container,
    .recent-activity,
    .quick-actions {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }

    .main-content .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header h2 {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .dashboard-card h4 {
        font-size: 1.1rem;
    }
}
