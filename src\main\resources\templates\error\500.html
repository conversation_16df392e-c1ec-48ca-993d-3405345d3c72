<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}">
<head>
    <title>서버 오류 - 한림공원 QR 체험</title>
    <meta name="description" content="서버에서 오류가 발생했습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-exclamation-triangle text-danger error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-danger">500</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">서버에서 오류가 발생했습니다</h1>
        <p class="error-description">
            일시적인 서버 문제로 인해 요청을 처리할 수 없습니다.<br>
            잠시 후 다시 시도해주세요.
        </p>

        <!-- 요청 URL 표시 (개발 환경에서만) -->
        <div class="error-details" th:if="${requestUrl != null}">
            <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <button type="button" class="btn btn-primary" onclick="reloadPage()">
                <i class="fas fa-redo me-2"></i>다시 시도하기
            </button>
            <a th:href="@{/}" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
        </div>
    </div>
</body>
</html>
