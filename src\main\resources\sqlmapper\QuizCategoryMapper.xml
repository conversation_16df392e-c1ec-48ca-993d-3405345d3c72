<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizCategoryMapper">

    <!-- 문제 카테고리 ResultMap -->
    <resultMap id="QuizCategoryResultMap" type="kr.wayplus.qr_hallimpark.model.QuizCategory">
        <id property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result property="description" column="description"/>
    </resultMap>

    <!-- 모든 문제 카테고리 목록 조회 -->
    <select id="selectQuizCategoryList" resultMap="QuizCategoryResultMap">
        SELECT 
            category_id,
            category_name,
            description
        FROM quiz_category
        ORDER BY category_id ASC
    </select>

    <!-- 카테고리 ID로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryById" parameterType="long" resultMap="QuizCategoryResultMap">
        SELECT 
            category_id,
            category_name,
            description
        FROM quiz_category
        WHERE category_id = #{categoryId}
    </select>

    <!-- 카테고리명으로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryByName" parameterType="string" resultMap="QuizCategoryResultMap">
        SELECT 
            category_id,
            category_name,
            description
        FROM quiz_category
        WHERE category_name = #{categoryName}
    </select>

    <!-- 문제 카테고리 등록 -->
    <insert id="insertQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO quiz_category (
            category_name,
            description
        ) VALUES (
            #{categoryName, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            #{description}
        )
    </insert>

    <!-- 문제 카테고리 수정 -->
    <update id="updateQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        UPDATE quiz_category 
        SET 
            category_name = #{categoryName, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            description = #{description}
        WHERE category_id = #{categoryId}
    </update>

    <!-- 문제 카테고리 삭제 -->
    <delete id="deleteQuizCategory" parameterType="long">
        DELETE FROM quiz_category 
        WHERE category_id = #{categoryId}
    </delete>

    <!-- 카테고리명 중복 체크 -->
    <select id="countByCategoryName" parameterType="string" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
    </select>

    <!-- 카테고리명 중복 체크 (수정 시 자기 자신 제외) -->
    <select id="countByCategoryNameExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND category_id != #{categoryId}
    </select>

</mapper>
